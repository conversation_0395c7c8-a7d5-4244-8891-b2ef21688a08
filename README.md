# Product Search

Here are all methods that let to search for a product in the database using part_no, specs etc.

Example how to run with part_no
### 
```python
from contextml import sql_utils
from contextai.Embeddings.hf_embeddings import HuggingFaceEmbeddingsToolkit
from contextai.VectorDatabase.faiss_vectordb import FaissVectorDatabase
from search_system_poc import ProductSearch

embeddings = HuggingFaceEmbeddingsToolkit()
sql_utils.set_sql_cred("your cred")
# A vector database can be found in \\147.114.32.164\import\machine_learning\datasets\model_reports\FlaviusM\vector_databases
# with the name specs_vdb_only_attribute_term
vectordb = FaissVectorDatabase.from_exist(embeddings, name="your vdb", vectordb_dir='your path')

product_search = ProductSearch(embeddings=embeddings, vectordb=vectordb)
#maybe you have to change the number used in the method
# to get a part_no run this
#sql_utils.query("""
# select product.product_id, product.product_category_id
#        from product
#        where product.product_category_id = 12""")
# and then:
# sql_utils.query("select product_id, part_no_id from product_part_no where product_id =  ") put a product id from above
products = product_search.get_similar_products_based_on_part_no(5339033)
print(products)
```