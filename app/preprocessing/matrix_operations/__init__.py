"""Matrix operations module for preprocessing.

This module contains functions for score matrix creation and product mapping
that were extracted from the Preprocessor class for better modularity.
"""

from .score_matrix_creation import (
    calculate_score_matrix_terms,
    calculate_score_matrix_measures,
    create_score_matrices,
)

from .product_mapping import (
    create_product_id_mapping,
    compute_product_mapping_parallel,
    save_product_mapping,
)

__all__ = [
    "calculate_score_matrix_terms",
    "calculate_score_matrix_measures",
    "create_score_matrices",
    "create_product_id_mapping",
    "compute_product_mapping_parallel",
    "save_product_mapping",
]
