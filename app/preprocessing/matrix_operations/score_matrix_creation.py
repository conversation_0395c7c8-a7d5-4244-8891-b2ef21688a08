"""Score matrix creation functions.

This module contains functions for creating score matrices for both term-based
and measure-based attributes, extracted from the Preprocessor class.
"""

import time
import numpy as np
import pickle
import json
import os
import gc
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from contextai.VectorDatabase.base_vectordb import BaseVectorDatabase
    from contextdb.Engine.base_database import BaseDatabaseEngine

# Import SQL queries
from ..queries.measure_queries import MEASURE_ORDERING_QUERY


def calculate_score_matrix_terms(
    attribute_id: int, batch_size: int, vdb: "BaseVectorDatabase", logger
) -> np.ndarray:
    """Creating the score matrix of the given attribute ID for term attributes."""
    log = logger.bind(attribute_id=attribute_id, attribute_type="TERM")

    log.info("Starting TERM attribute processing", attribute_id=attribute_id)
    start_time = time.time()

    docs = vdb.search_by_doc(attribute_id=attribute_id, ids_only=False)
    if not docs:
        log.warning("No documents found for TERM attribute", attribute_id=attribute_id)
        return np.array([])

    # Create empty score matrix
    doc_ids = np.array(list(docs.keys()))
    size = doc_ids.shape[0]
    log.info(
        "Creating TERM score matrix",
        attribute_id=attribute_id,
    )

    score_matrix = np.zeros(shape=(size, size))

    # create arrays for doc information
    doc_arr = np.array(list(docs.values()))
    term_orders = np.vectorize(lambda x: x.metadata["term_order"])(doc_arr)
    texts = np.vectorize(lambda x: x.text)(doc_arr)
    doc_orders = np.argsort(term_orders)
    texts = texts[doc_orders]
    doc_arr = doc_arr[doc_orders]
    doc_ids = doc_ids[doc_orders]
    term_orders = term_orders[doc_orders]

    # Gather the terms and their vectors

    # Calculating batches
    num_batch = (
        size // batch_size
        if (size // batch_size) == (size / batch_size)
        else (size // batch_size) + 1
    )
    batches = [
        (b * batch_size, min(size, (b + 1) * batch_size)) for b in range(num_batch)
    ]

    # doing batch search on vector database
    from ..utils.progress_tracker import track_batch_processing

    index_map = dict(zip(doc_ids, term_orders))
    batch_progress = track_batch_processing(
        batches, f"TERM vector search for attr {attribute_id}"
    )

    try:
        for batch in batches:
            vectors = vdb.get_vectors_by_ids(doc_ids[batch[0] : batch[1]]).astype(
                dtype=np.float32
            )
            ids, scores = vdb._batch_search_by_vectors(
                vectors=vectors, scope_ids=doc_ids, top_k=size
            )
            del vectors
            # Get indexes of results in the score matrix
            get_index = np.vectorize(lambda x: index_map[x])
            indexes = get_index(ids)
            first_indexes = np.arange(batch[0], batch[1])
            first_indexes = np.tile(first_indexes.reshape(-1, 1), (1, scores.shape[1]))
            score_matrix[first_indexes, indexes] = scores

            batch_progress.update(1, batch_range=f"{batch[0]}-{batch[1]}")
    finally:
        batch_progress.close()

    log.info("Finalizing TERM score matrix", attribute_id=attribute_id)
    score_matrix = (score_matrix + score_matrix.T) / 2

    elapsed_time = time.time() - start_time
    log.info(
        "TERM score matrix calculation completed",
        attribute_id=attribute_id,
        matrix_shape=score_matrix.shape,
        elapsed_time_seconds=round(elapsed_time, 2),
    )
    return score_matrix


def calculate_score_matrix_measures(
    attribute_id: int,
    local_sqlite_db: "BaseDatabaseEngine",
    logger,
    max_chunk_size: int = 8000,
) -> np.ndarray:
    """Calculate the score matrix for measure attributes directly from the database.

    This method maintains the term ordering while working directly with NumPy arrays
    for better performance and memory efficiency.

    Args:
        attribute_id: The attribute ID to calculate the score matrix for
        local_sqlite_db: Local SQLite database connection
        logger: Logger instance
        max_chunk_size: Maximum chunk size for processing large matrices

    Returns:
        np.ndarray: The score matrix
    """
    log = logger.bind(attribute_id=attribute_id, attribute_type="MEASURE")

    log.info("Starting MEASURE attribute processing", attribute_id=attribute_id)
    start_time = time.time()

    log.info("Fetching MEASURE values from database", attribute_id=attribute_id)
    result = local_sqlite_db.query(
        MEASURE_ORDERING_QUERY.format(attribute_id=attribute_id)
    )

    if result.empty:
        log.warning(
            "No measure data found for MEASURE attribute", attribute_id=attribute_id
        )
        return np.array([])

    values = result[["normalised_measure"]].to_numpy().flatten()
    n = len(values)
    del result
    gc.collect()

    # For very small matrices, use direct calculation
    if n <= max_chunk_size:
        log.info(
            "Using direct calculation for small MEASURE matrix",
            attribute_id=attribute_id,
        )

        # Create the meshgrid
        values_i, values_j = np.meshgrid(values, values, indexing="ij")

        # Calculate conditions
        both_nonzero = (values_i != 0) & (values_j != 0)
        both_zero = (values_i == 0) & (values_j == 0)

        # Initialize score matrix
        score_matrix = np.zeros((n, n), dtype=np.float32)

        # Set diagonal to 1.0 (same value compared to itself)
        np.fill_diagonal(score_matrix, 1.0)

        # Calculate ratios only where both values are non-zero
        if np.any(both_nonzero):
            ratio1 = values_i[both_nonzero] / values_j[both_nonzero]
            ratio2 = values_j[both_nonzero] / values_i[both_nonzero]
            score_matrix[both_nonzero] = np.minimum(ratio1, ratio2)
            del ratio1, ratio2

        # Set score to 1.0 where both values are zero
        score_matrix[both_zero] = 1.0

        # Clean up memory
        del values_i, values_j, both_nonzero, both_zero
        gc.collect()

        elapsed_time = time.time() - start_time
        log.info(
            "MEASURE score matrix calculation completed (direct)",
            attribute_id=attribute_id,
            matrix_shape=score_matrix.shape,
            elapsed_time_seconds=round(elapsed_time, 2),
        )
        return score_matrix

    # For large matrices, process in chunks

    # Create empty score matrix - only allocate once
    score_matrix = np.zeros((n, n), dtype=np.float32)

    # Set diagonal to 1.0 (same value compared to itself)
    np.fill_diagonal(score_matrix, 1.0)

    # Process in chunks
    chunk_size = min(max_chunk_size, n)
    total_chunks = (n + chunk_size - 1) // chunk_size
    processed_chunks = 0

    for i in range(0, n, chunk_size):
        end_i = min(i + chunk_size, n)
        values_i = values[i:end_i]

        # Process each chunk against all other chunks
        for j in range(
            i, n, chunk_size
        ):  # Start from i to avoid redundant calculations
            end_j = min(j + chunk_size, n)
            values_j = values[j:end_j]

            # Create small meshgrid for this chunk pair
            vi, vj = np.meshgrid(values_i, values_j, indexing="ij")

            # Calculate similarity for this chunk
            both_nonzero = (vi != 0) & (vj != 0)
            both_zero = (vi == 0) & (vj == 0)

            # Initialize chunk scores directly, avoid intermediate array
            chunk_scores = np.zeros((end_i - i, end_j - j), dtype=np.float32)

            # Set score to 1.0 where both values are zero
            chunk_scores[both_zero] = 1.0

            # Calculate ratios only where both values are non-zero
            if np.any(both_nonzero):
                ratio1 = vi[both_nonzero] / vj[both_nonzero]
                ratio2 = vj[both_nonzero] / vi[both_nonzero]
                chunk_scores[both_nonzero] = np.minimum(ratio1, ratio2)
                del ratio1, ratio2

            # Update the main score matrix
            score_matrix[i:end_i, j:end_j] = chunk_scores

            # Mirror for symmetry (only if not on diagonal)
            if i != j:
                score_matrix[j:end_j, i:end_i] = chunk_scores.T

            # Clean up memory after each chunk
            del vi, vj, both_nonzero, both_zero, chunk_scores
            gc.collect()

            processed_chunks += 1
            if processed_chunks % 10 == 0:
                log.info(
                    "MEASURE chunked processing progress",
                    attribute_id=attribute_id,
                    processed_chunks=processed_chunks,
                    total_chunks=total_chunks,
                    progress_percent=round((processed_chunks / total_chunks) * 100, 1),
                )

    # Clean up remaining memory
    del values, values_i, values_j
    gc.collect()

    elapsed_time = time.time() - start_time
    log.info(
        "MEASURE score matrix calculation completed (chunked)",
        attribute_id=attribute_id,
        matrix_shape=score_matrix.shape,
        total_chunks_processed=processed_chunks,
        elapsed_time_seconds=round(elapsed_time, 2),
    )
    return score_matrix


def create_score_matrices(
    score_matrix_dir: str,
    attribute_ids_size_term: dict,
    attribute_ids_size_measure: dict,
    vdb: "BaseVectorDatabase",
    local_sqlite_db: "BaseDatabaseEngine",
    logger,
    batch_size: int = 4000,
) -> None:
    """Create score matrices for term and measure attributes."""
    from ..utils.progress_tracker import track_matrix_creation

    # Counts of terms by attribute ID
    os.makedirs(score_matrix_dir, exist_ok=True)
    logger.info("Score matrix folder created", directory=score_matrix_dir)

    score_matrix_size = {}

    # Prepare data for progress tracking
    term_data = [
        (attr_id, size, "term") for attr_id, size in attribute_ids_size_term.items()
    ]
    measure_data = [
        (attr_id, size, "measure")
        for attr_id, size in attribute_ids_size_measure.items()
    ]
    all_attributes = term_data + measure_data

    # Create overall progress tracker
    progress = track_matrix_creation(all_attributes, "score")

    try:
        # Create the score matrix for term attributes
        if attribute_ids_size_term:
            logger.info(
                "Starting TERM attributes processing",
                total_term_attributes=len(attribute_ids_size_term),
            )

        for attribute_id, size in attribute_ids_size_term.items():
            progress.update(1, current=f"term_{attribute_id}", size=size)
            logger.info(
                "Processing TERM attribute",
                attribute_id=attribute_id,
                size=size,
                attribute_type="TERM",
            )

            score_matrix = calculate_score_matrix_terms(
                attribute_id=attribute_id, batch_size=batch_size, vdb=vdb, logger=logger
            )
            with open(f"{score_matrix_dir}/{attribute_id}.pkl", "wb") as f:
                pickle.dump(score_matrix, f)
                score_matrix_size[int(attribute_id)] = int(size)
                del score_matrix

            logger.info(
                "TERM attribute completed and saved",
                attribute_id=attribute_id,
                file_path=f"{score_matrix_dir}/{attribute_id}.pkl",
            )

        # Create the score matrix for measure attributes
        if attribute_ids_size_measure:
            logger.info(
                "Starting MEASURE attributes processing",
                total_measure_attributes=len(attribute_ids_size_measure),
            )

        for attribute_id, size in attribute_ids_size_measure.items():
            progress.update(1, current=f"measure_{attribute_id}", size=size)
            logger.info(
                "Processing MEASURE attribute",
                attribute_id=attribute_id,
                size=size,
                attribute_type="MEASURE",
            )

            score_matrix = calculate_score_matrix_measures(
                attribute_id=attribute_id,
                local_sqlite_db=local_sqlite_db,
                logger=logger,
            )
            with open(f"{score_matrix_dir}/{attribute_id}.pkl", "wb") as f:
                pickle.dump(score_matrix, f)
                score_matrix_size[int(attribute_id)] = int(size)
                del score_matrix

            logger.info(
                "MEASURE attribute completed and saved",
                attribute_id=attribute_id,
                file_path=f"{score_matrix_dir}/{attribute_id}.pkl",
            )
    finally:
        progress.close()

    score_matrix_size_json = os.path.join(
        os.path.dirname(score_matrix_dir), "score_matrix_size.json"
    )
    with open(score_matrix_size_json, "w") as f:
        json.dump(score_matrix_size, f)
