"""
SQL queries for attribute-related operations during preprocessing.
"""

# Query to get attribute order in their respective category
ATTRIBUTES_BY_CATEGORY_QUERY = """
SELECT *,
    ROW_NUMBER() OVER(PARTITION BY product_category_id ORDER BY attribute_id) - 1 AS pcat_attribute_order
FROM (
    SELECT DISTINCT(attribute_id), product_category_id
    FROM attribute_group_attribute
    {where_clause}
)
sub
"""

# Query to get attribute IDs by product category
ATTRIBUTE_IDS_BY_PCAT_QUERY = """
SELECT product_category_id, attribute_id, pcat_attribute_order 
FROM {attributes_by_category_table} 
ORDER BY 1, 3
"""

# Query to get distinct product category IDs from product table
DISTINCT_PCAT_IDS_FROM_PRODUCT_QUERY = """
SELECT DISTINCT(product_category_id) 
FROM product
"""

# Query to count products by category ID
COUNT_PRODUCTS_BY_PCAT_QUERY = """
SELECT COUNT(product_id) 
FROM product 
WHERE product_category_id = {pcat_id}
"""

# Query to get attribute terms for vector database
ATTRIBUTE_TERMS_FOR_VDB_QUERY = """
WITH ate AS (
    SELECT attribute_term_id, attribute_id, term, 'term' AS type
    FROM attribute_term
)
SELECT ROW_NUMBER() OVER (PARTITION BY ate.attribute_id ORDER BY ate.term) - 1 AS term_order,
ate.*
FROM ate
WHERE ate.attribute_id IN (SELECT DISTINCT attribute_id FROM {attributes_by_category_table})
"""
