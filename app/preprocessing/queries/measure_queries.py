"""
SQL queries for measure-related operations during preprocessing.
"""

# Base query to create mtbase data
MTBASE_CREATION_QUERY = """
WITH cats AS (
    SELECT DISTINCT product_category_id
    FROM attribute_group_attribute
),
ub AS (
    SELECT 
        mu.measure_id,
        mu.unit AS base_unit
    FROM measure_unit mu
    WHERE mu.base
),
attrs AS (
    SELECT 
        cat.attribute_id,
        at.type AS attribute_type
    FROM attribute_group_attribute cat
    LEFT JOIN attribute at USING(attribute_id)
    WHERE at.type IN ('measure', 'simple_measure')
    GROUP BY 1, 2
),
mtbase_all AS (
    SELECT
        p.product_id,
        cats.product_category_id,
        pa.attribute_id,
        a.name AS attribute,
        a.type AS attribute_type,
        CASE 
            WHEN mu.base_multiplier IS NOT NULL THEN pa.measure * mu.base_multiplier 
            ELSE pa.measure 
        END AS normalised_measure,
        COALESCE(ub.base_unit, mu.unit) AS base_unit
    FROM product_attribute pa
    LEFT JOIN product p USING(product_id)
    LEFT JOIN attribute a USING(attribute_id)
    JOIN cats USING(product_category_id)
    JOIN attrs USING(attribute_id)
    LEFT JOIN measure_unit mu USING(measure_unit_id)
    LEFT JOIN ub ON ub.measure_id = mu.measure_id
    WHERE pa.measure IS NOT NULL
)
SELECT * FROM mtbase_all
"""

# Query for measure analysis and statistics
MEASURE_ANALYSIS_QUERY = """
WITH unit_counts AS (
    SELECT
        attribute_id,
        COUNT(DISTINCT base_unit) AS unit_count
    FROM mtbase_all
    GROUP BY attribute_id
),
measure_stats AS (
    SELECT
        m.attribute_id,
        uc.unit_count,
        COUNT(DISTINCT m.normalised_measure) AS term_count
    FROM mtbase_all m
    LEFT JOIN unit_counts uc USING(attribute_id)
    GROUP BY attribute_id, unit_count
)
SELECT * FROM measure_stats
WHERE term_count >= 1 OR unit_count <= 1
ORDER BY term_count DESC
"""

# Query for term counts analysis
TERM_COUNTS_ANALYSIS_QUERY = """
WITH attrs AS (
    SELECT cat.attribute_id
    FROM attribute_group_attribute cat
    LEFT JOIN attribute at USING(attribute_id)
    WHERE at.type = 'term'
    GROUP BY 1
),
term_counts AS (
    SELECT
        attrs.attribute_id,
        COUNT(DISTINCT at.term) AS term_count,
        1 AS unit_count
    FROM attrs
    LEFT JOIN attribute_term at USING(attribute_id)
    GROUP BY 1
)
SELECT * FROM term_counts
WHERE term_count > 1
ORDER BY term_count DESC
"""

# Query for measure ordering and score matrix calculation
MEASURE_ORDERING_QUERY = """
WITH measure_ordering AS (
    SELECT
        product_id,
        normalised_measure,
        product_category_id,
        ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY normalised_measure) - 1 AS term_order
    FROM mtbase_all
    WHERE attribute_id = {attribute_id}
    GROUP BY attribute_id, normalised_measure
)
SELECT product_id, normalised_measure, term_order
FROM measure_ordering
GROUP BY product_id
ORDER BY term_order
"""
