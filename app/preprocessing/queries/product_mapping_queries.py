"""
SQL queries for product mapping operations during preprocessing.
Optimized for performance with consolidated queries and reduced database round trips.
"""

# Query to get attribute order for a specific product category
ATTRIBUTE_ORDER_BY_PCAT_QUERY = """
SELECT attribute_id, pcat_attribute_order
FROM attribute_order
WHERE product_category_id = {product_category_id}
ORDER BY pcat_attribute_order
"""

# Query to get attribute term counts for a specific product category
ATTRIBUTE_TERM_COUNT_BY_PCAT_QUERY = """
SELECT attribute_id, term_count
FROM combined_count
LEFT JOIN attribute_order ao USING (attribute_id)
WHERE ao.product_category_id = {product_category_id}
"""

# Query to get product categories for processing
PRODUCT_CATEGORIES_FOR_PROCESSING_QUERY = """
SELECT product_category_id
FROM {attributes_by_category_table}
GROUP BY product_category_id
"""

# Query to get all product IDs for a specific category
ALL_PRODUCTS_BY_CATEGORY_QUERY = """
SELECT DISTINCT product_id
FROM product
WHERE product_category_id = {product_category_id}
"""

# OPTIMIZED: Consolidated query to get all basic data for a product category in one call
CONSOLIDATED_CATEGORY_DATA_QUERY = """
WITH attribute_data AS (
    SELECT
        ao.attribute_id,
        ao.pcat_attribute_order,
        COALESCE(cc.term_count, 0) as term_count
    FROM attribute_order ao
    LEFT JOIN combined_count cc USING (attribute_id)
    WHERE ao.product_category_id = {product_category_id}
),
product_data AS (
    SELECT DISTINCT product_id
    FROM product
    WHERE product_category_id = {product_category_id}
)
SELECT
    'attribute' as data_type,
    CAST(attribute_id AS TEXT) as id,
    pcat_attribute_order as order_value,
    term_count as count_value,
    NULL as product_id
FROM attribute_data
UNION ALL
SELECT
    'product' as data_type,
    CAST(product_id AS TEXT) as id,
    NULL as order_value,
    NULL as count_value,
    product_id
FROM product_data
ORDER BY data_type, order_value, id
"""

# OPTIMIZED: Simplified measure data query with better performance
MEASURE_DATA_WITH_ORDERING_QUERY = """
WITH measure_data AS (
    SELECT DISTINCT
        m.product_id,
        m.product_category_id,
        m.attribute_id,
        m.normalised_measure,
        ROW_NUMBER() OVER (
            PARTITION BY m.product_id, m.attribute_id
            ORDER BY m.normalised_measure
        ) - 1 AS term_order
    FROM mtbase_all m
    WHERE m.product_category_id = {product_category_id}
    AND m.attribute_id IN ({attr_id_str})
),
measure_max_sizes AS (
    SELECT
        attribute_id,
        MAX(term_order) + 1 AS num_terms
    FROM measure_data
    GROUP BY attribute_id
)
SELECT
    md.product_id,
    md.product_category_id,
    md.attribute_id,
    md.normalised_measure,
    md.term_order,
    ms.num_terms,
    'measure' AS value_type
FROM measure_data md
LEFT JOIN measure_max_sizes ms ON md.attribute_id = ms.attribute_id
ORDER BY md.product_id, md.attribute_id;
"""

# Complex query for measure data with proper term ordering (LEGACY - kept for compatibility)
MEASURE_DATA_WITH_ORDERING_QUERY_LEGACY = """
WITH measure_ordering AS (
SELECT
    m.product_id,
    m.product_category_id,
    m.attribute_id,
    m.normalised_measure,
    ROW_NUMBER() OVER (
        PARTITION BY m.product_id
        ORDER BY m.normalised_measure
    ) - 1 AS term_order
FROM mtbase_all m
WHERE m.product_category_id = {product_category_id}
AND m.attribute_id IN ({attr_id_str})
),
measure_max_sizes AS (
    SELECT
        attribute_id,
        MAX(term_order) + 1 AS num_terms
    FROM measure_ordering
    GROUP BY attribute_id
)
SELECT
    m.product_id,
    m.product_category_id,
    m.attribute_id,
    m.normalised_measure,
    mo.term_order,
    ms.num_terms,
    'measure' AS value_type
FROM mtbase_all m
LEFT JOIN measure_ordering mo
    ON m.attribute_id = mo.attribute_id
    AND m.normalised_measure = mo.normalised_measure
    AND m.product_id = mo.product_id
LEFT JOIN measure_max_sizes ms ON m.attribute_id = ms.attribute_id
WHERE m.product_category_id = {product_category_id}
AND m.attribute_id IN ({attr_id_str})
ORDER BY m.product_id, m.product_category_id;
"""

# OPTIMIZED: Simplified term data query with better performance
TERM_DATA_WITH_ORDERING_QUERY = """
WITH term_ordering AS (
    SELECT
        at.attribute_term_id,
        at.attribute_id,
        at.term,
        ROW_NUMBER() OVER (PARTITION BY at.attribute_id ORDER BY at.term) - 1 AS term_order
    FROM attribute_term at
    WHERE at.attribute_id IN (
        SELECT DISTINCT attribute_id
        FROM {attributes_by_category_table}
        WHERE product_category_id = {product_category_id}
    )
),
term_max_sizes AS (
    SELECT
        attribute_id,
        MAX(term_order) + 1 AS num_terms
    FROM term_ordering
    GROUP BY attribute_id
),
term_data AS (
    SELECT
        p.product_id,
        p.product_category_id,
        ao.attribute_id,
        a.type,
        ao.pcat_attribute_order,
        pa.product_attribute_id,
        at.term,
        'term' AS value_type
    FROM product p
    INNER JOIN {attributes_by_category_table} ao ON p.product_category_id = ao.product_category_id
    INNER JOIN attribute a ON ao.attribute_id = a.attribute_id
    LEFT JOIN product_attribute pa ON (p.product_id = pa.product_id AND ao.attribute_id = pa.attribute_id)
    LEFT JOIN attribute_term at ON (pa.attribute_term_id = at.attribute_term_id)
    WHERE p.product_category_id = {product_category_id}
    AND a.type = 'term'
)
SELECT
    td.product_id,
    td.product_category_id,
    td.attribute_id,
    td.type,
    td.pcat_attribute_order,
    td.product_attribute_id,
    td.term,
    td.value_type,
    tor.term_order,
    tms.num_terms
FROM term_data td
LEFT JOIN term_ordering tor ON (td.term = tor.term AND td.attribute_id = tor.attribute_id)
LEFT JOIN term_max_sizes tms ON (td.attribute_id = tms.attribute_id)
ORDER BY td.product_id, td.pcat_attribute_order;
"""

# OPTIMIZED: Combined measure and term data query to reduce database calls
COMBINED_MEASURE_TERM_DATA_QUERY = """
WITH measure_data AS (
    SELECT DISTINCT
        m.product_id,
        m.product_category_id,
        m.attribute_id,
        m.normalised_measure as value,
        ROW_NUMBER() OVER (
            PARTITION BY m.product_id, m.attribute_id
            ORDER BY m.normalised_measure
        ) - 1 AS term_order,
        'measure' AS value_type
    FROM mtbase_all m
    WHERE m.product_category_id = {product_category_id}
    AND m.attribute_id IN ({attr_id_str})
),
term_ordering AS (
    SELECT
        at.attribute_id,
        at.term,
        ROW_NUMBER() OVER (PARTITION BY at.attribute_id ORDER BY at.term) - 1 AS term_order
    FROM attribute_term at
    WHERE at.attribute_id IN ({attr_id_str})
),
term_data AS (
    SELECT
        p.product_id,
        p.product_category_id,
        ao.attribute_id,
        at.term as value,
        tor.term_order,
        'term' AS value_type
    FROM product p
    INNER JOIN {attributes_by_category_table} ao ON p.product_category_id = ao.product_category_id
    INNER JOIN attribute a ON ao.attribute_id = a.attribute_id
    LEFT JOIN product_attribute pa ON (p.product_id = pa.product_id AND ao.attribute_id = pa.attribute_id)
    LEFT JOIN attribute_term at ON (pa.attribute_term_id = at.attribute_term_id)
    LEFT JOIN term_ordering tor ON (at.term = tor.term AND ao.attribute_id = tor.attribute_id)
    WHERE p.product_category_id = {product_category_id}
    AND a.type = 'term'
    AND ao.attribute_id IN ({attr_id_str})
),
max_terms AS (
    SELECT attribute_id, MAX(term_order) + 1 AS num_terms
    FROM (
        SELECT attribute_id, term_order FROM measure_data
        UNION ALL
        SELECT attribute_id, term_order FROM term_data WHERE term_order IS NOT NULL
    ) combined
    GROUP BY attribute_id
)
SELECT
    md.product_id,
    md.product_category_id,
    md.attribute_id,
    md.value,
    md.term_order,
    mt.num_terms,
    md.value_type
FROM measure_data md
LEFT JOIN max_terms mt ON md.attribute_id = mt.attribute_id
UNION ALL
SELECT
    td.product_id,
    td.product_category_id,
    td.attribute_id,
    td.value,
    COALESCE(td.term_order, mt.num_terms) as term_order,
    mt.num_terms,
    td.value_type
FROM term_data td
LEFT JOIN max_terms mt ON td.attribute_id = mt.attribute_id
ORDER BY 1, 3;
"""
