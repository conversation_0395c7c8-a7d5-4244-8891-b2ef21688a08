"""
Simple progress tracking utilities for preprocessing operations.
Provides TQDM integration with memory monitoring and rate calculations.
"""

import time
import psutil
from tqdm import tqdm
from typing import Optional, Dict, Any
import structlog

logger = structlog.get_logger(__name__)


class ProgressTracker:
    """Simple progress tracker with TQDM, memory monitoring, and rate calculation."""
    
    def __init__(self, total: int, desc: str, unit: str = "items"):
        """Initialize progress tracker.
        
        Args:
            total: Total number of items to process
            desc: Description for the progress bar
            unit: Unit name for items (default: "items")
        """
        self.total = total
        self.desc = desc
        self.unit = unit
        self.start_time = time.time()
        self.start_memory = psutil.Process().memory_info().rss / (1024 * 1024)  # MB
        
        # Create TQDM progress bar with custom format
        self.pbar = tqdm(
            total=total,
            desc=desc,
            unit=unit,
            bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}] {postfix}'
        )
        
        logger.info("Progress tracking started", 
                   desc=desc, total=total, unit=unit, 
                   start_memory_mb=self.start_memory)
    
    def update(self, n: int = 1, **kwargs):
        """Update progress and optionally add custom info.
        
        Args:
            n: Number of items processed (default: 1)
            **kwargs: Additional info to display in postfix
        """
        self.pbar.update(n)
        
        # Add memory info to postfix
        current_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        memory_diff = current_memory - self.start_memory
        
        postfix = {
            'mem': f'{current_memory:.0f}MB',
            'Δmem': f'{memory_diff:+.0f}MB'
        }
        postfix.update(kwargs)
        
        self.pbar.set_postfix(postfix)
    
    def close(self):
        """Close progress bar and log final stats."""
        elapsed = time.time() - self.start_time
        final_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        memory_diff = final_memory - self.start_memory
        
        self.pbar.close()
        
        logger.info("Progress tracking completed",
                   desc=self.desc,
                   total_processed=self.total,
                   elapsed_seconds=elapsed,
                   rate_per_minute=self.total / elapsed * 60 if elapsed > 0 else 0,
                   memory_change_mb=memory_diff,
                   final_memory_mb=final_memory)


def track_matrix_creation(attributes_data: list, matrix_type: str = "matrix"):
    """Create progress tracker for matrix creation.
    
    Args:
        attributes_data: List of (attribute_id, size, type) tuples
        matrix_type: Type of matrices being created
        
    Returns:
        ProgressTracker instance
    """
    total_attributes = len(attributes_data)
    desc = f"Creating {matrix_type} matrices"
    
    return ProgressTracker(total_attributes, desc, "attrs")


def track_category_processing(category_ids: list):
    """Create progress tracker for category processing.
    
    Args:
        category_ids: List of category IDs to process
        
    Returns:
        ProgressTracker instance
    """
    total_categories = len(category_ids)
    desc = "Processing product categories"
    
    return ProgressTracker(total_categories, desc, "cats")


def track_batch_processing(batches: list, operation: str):
    """Create progress tracker for batch processing.
    
    Args:
        batches: List of batches to process
        operation: Description of the operation
        
    Returns:
        ProgressTracker instance
    """
    total_batches = len(batches)
    desc = f"{operation}"
    
    return ProgressTracker(total_batches, desc, "batches")
