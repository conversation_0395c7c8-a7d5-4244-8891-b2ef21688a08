"""
Pydantic models for product search API requests and responses.

This module defines the data models used for API request validation
and response serialization for product search endpoints.
"""

from typing import Dict, List, Optional

try:
    from pydantic import BaseModel, Field, validator
except ImportError:
    # Fallback for when pydantic is not available
    class BaseModel:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

    def Field(*args, **kwargs):
        return None

    def validator(*args, **kwargs):
        def decorator(func):
            return func

        return decorator


class ProductSearchRequest(BaseModel):
    """Request model for product similarity search by product ID."""

    query_product_ids: List[int] = Field(
        ..., description="List of product IDs to use as search queries", min_items=1
    )
    database: Optional[str] = Field(
        default="",
        description="Database identifier (empty string uses default database)",
    )
    weights: Optional[Dict[str, float]] = Field(
        default=None,
        description="Optional attribute weights for similarity calculation",
    )
    top_k: int = Field(
        default=5, description="Number of similar products to return", ge=1, le=100
    )
    scope_product_id: Optional[List[int]] = Field(
        default=None, description="Optional list of product IDs to scope the search"
    )

    @validator("query_product_ids")
    def validate_query_product_ids(cls, v):
        """Validate that all product IDs are positive integers."""
        if not all(isinstance(pid, int) and pid > 0 for pid in v):
            raise ValueError("All product IDs must be positive integers")
        return v

    @validator("scope_product_id")
    def validate_scope_product_ids(cls, v):
        """Validate that all scope product IDs are positive integers."""
        if v is not None and not all(isinstance(pid, int) and pid > 0 for pid in v):
            raise ValueError("All scope product IDs must be positive integers")
        return v

    @validator("weights")
    def validate_weights(cls, v):
        """Validate that weights are properly formatted."""
        if v is not None:
            for _, value in v.items():
                if not isinstance(value, (int, float)):
                    raise ValueError("All weight values must be numeric")
                if value < 0:
                    raise ValueError("All weight values must be non-negative")
        return v


class ProductSpecsSearchRequest(BaseModel):
    """Request model for product similarity search by specifications."""

    specs: List[Dict[str, str]] = Field(
        ..., description="List of product specifications for search", min_items=1
    )
    product_category_id: int = Field(..., description="Product category ID", gt=0)
    database: Optional[str] = Field(
        default="",
        description="Database identifier (empty string uses default database)",
    )
    weights: Optional[Dict[str, float]] = Field(
        default=None,
        description="Optional attribute weights for similarity calculation",
    )
    top_k: int = Field(
        default=5, description="Number of similar products to return", ge=1, le=100
    )
    scope_query: Optional[str] = Field(
        default=None, description="Optional SQL query to scope the search"
    )

    @validator("specs")
    def validate_specs(cls, v):
        """Validate that specs are properly formatted."""
        for spec in v:
            if not isinstance(spec, dict):
                raise ValueError("Each spec must be a dictionary")
            if not spec:
                raise ValueError("Spec dictionaries cannot be empty")
        return v


class ProductSearchResult(BaseModel):
    """Individual product search result."""

    top_product_ids: List[int] = Field(description="List of similar product IDs")
    similarity_scores: List[float] = Field(
        description="Similarity scores for each product"
    )
    score_by_attribute_by_result: List[List[float]] = Field(
        description="Detailed attribute scores for each result"
    )


class ProductSearchResponse(BaseModel):
    """Response model for product search operations."""

    results: Dict[int, ProductSearchResult] = Field(
        description="Search results keyed by query product ID"
    )
    metadata: Optional[Dict] = Field(
        default=None, description="Optional metadata about the search operation"
    )


class DatabaseListResponse(BaseModel):
    """Response model for available databases."""

    databases: List[str] = Field(description="List of available database identifiers")


class ValidationErrorResponse(BaseModel):
    """Response model for validation errors."""

    detail: str = Field(description="Error message describing the validation failure")
    errors: Optional[List[str]] = Field(
        default=None, description="List of specific validation errors"
    )


class SearchMetadata(BaseModel):
    """Metadata for search operations."""

    query_count: int = Field(description="Number of query products")
    category_id: int = Field(description="Product category ID")
    processing_time_seconds: float = Field(description="Processing time in seconds")
    total_results: int = Field(description="Total number of results returned")
    database_used: str = Field(description="Database identifier used for search")
