"""
Database service for handling database discovery and file operations.

This service handles:
- Database discovery and listing
- Database file operations
- Configuration file reading
- Database availability checks
"""

import json
import os
from typing import Dict, List, Optional
from fastapi import HTTPException

from app.utils.logging import setup_logging

logger = setup_logging()


class DatabaseService:
    """Service for database operations and discovery."""
    
    def __init__(self, base_directory: str):
        self.base_directory = base_directory
    
    def get_available_databases(self) -> Dict:
        """
        Get list of available databases from databases.json file.
        
        Returns:
            Dictionary containing available databases
            
        Raises:
            HTTPException: If databases.json file cannot be read
        """
        try:
            databases_file = os.path.join(self.base_directory, "databases.json")
            
            if not os.path.exists(databases_file):
                logger.error("databases.file_not_found", path=databases_file)
                raise HTTPException(
                    status_code=404, 
                    detail="Databases configuration file not found"
                )
            
            with open(databases_file, "r") as f:
                databases = json.load(f)
            
            logger.info("databases.retrieved", count=len(databases) if isinstance(databases, list) else "unknown")
            return databases
            
        except json.JSONDecodeError as e:
            logger.error("databases.json_decode_error", error=str(e))
            raise HTTPException(
                status_code=500,
                detail="Invalid databases configuration file format"
            )
        except Exception as e:
            logger.error("databases.retrieval_failed", error=str(e))
            raise HTTPException(
                status_code=500,
                detail=f"Failed to retrieve databases: {str(e)}"
            )
    
    def get_database_info(self, database: str) -> Dict:
        """
        Get information about a specific database.
        
        Args:
            database: Database identifier
            
        Returns:
            Dictionary containing database information
            
        Raises:
            HTTPException: If database info cannot be retrieved
        """
        try:
            db_path = os.path.join(self.base_directory, database)
            info_file = os.path.join(db_path, "info.json")
            
            if not os.path.exists(info_file):
                logger.error("database.info_not_found", database=database, path=info_file)
                raise HTTPException(
                    status_code=404,
                    detail=f"Database info file not found for: {database}"
                )
            
            with open(info_file, "r") as f:
                info = json.load(f)
            
            logger.info("database.info_retrieved", database=database)
            return info
            
        except json.JSONDecodeError as e:
            logger.error("database.info_json_error", database=database, error=str(e))
            raise HTTPException(
                status_code=500,
                detail=f"Invalid database info file format for: {database}"
            )
        except Exception as e:
            logger.error("database.info_retrieval_failed", database=database, error=str(e))
            raise HTTPException(
                status_code=500,
                detail=f"Failed to retrieve database info for {database}: {str(e)}"
            )
    
    def resolve_default_database(self) -> str:
        """
        Resolve the default database from info.json.
        
        Returns:
            Default database identifier
            
        Raises:
            HTTPException: If default database cannot be resolved
        """
        try:
            info_file = os.path.join(self.base_directory, "info.json")
            
            if not os.path.exists(info_file):
                logger.error("default_database.info_not_found", path=info_file)
                raise HTTPException(
                    status_code=404,
                    detail="Default database info file not found"
                )
            
            with open(info_file, "r") as f:
                info = json.load(f)
            
            default_db = info.get("database", "")
            if not default_db:
                logger.error("default_database.not_specified")
                raise HTTPException(
                    status_code=400,
                    detail="Default database not specified in configuration"
                )
            
            logger.info("default_database.resolved", database=default_db)
            return default_db
            
        except json.JSONDecodeError as e:
            logger.error("default_database.json_error", error=str(e))
            raise HTTPException(
                status_code=500,
                detail="Invalid default database info file format"
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error("default_database.resolution_failed", error=str(e))
            raise HTTPException(
                status_code=500,
                detail=f"Failed to resolve default database: {str(e)}"
            )
    
    def validate_database_exists(self, database: str) -> bool:
        """
        Validate that a database directory exists.
        
        Args:
            database: Database identifier
            
        Returns:
            True if database exists, False otherwise
        """
        if not database:
            return False
            
        db_path = os.path.join(self.base_directory, database)
        exists = os.path.exists(db_path) and os.path.isdir(db_path)
        
        logger.debug("database.existence_check", database=database, exists=exists)
        return exists
    
    def get_database_path(self, database: str) -> str:
        """
        Get the full path to a database directory.
        
        Args:
            database: Database identifier
            
        Returns:
            Full path to database directory
        """
        return os.path.join(self.base_directory, database)
