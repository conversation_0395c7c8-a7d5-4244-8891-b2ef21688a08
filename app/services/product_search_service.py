"""
Product search service for handling product similarity search operations.

This service handles all product search business logic including:
- Similar product searches by product ID
- Similar product searches by specifications
- Weight processing and transformation
- Search result processing
"""

import asyncio
import time
from typing import Dict, List, Optional, Union

from app.core.processor_registry import registry
from app.utils.logging import setup_logging

logger = setup_logging()


class ProductSearchService:
    """Service for handling product search operations."""
    
    def __init__(self):
        pass
    
    async def search_similar_products_by_id(
        self,
        query_product_ids: List[int],
        category: int,
        database: str,
        weights: Optional[Dict[str, float]] = None,
        top_k: int = 5,
        scope_product_ids: Optional[List[int]] = None,
    ) -> Dict:
        """
        Search for similar products using existing product IDs.
        
        Args:
            query_product_ids: List of product IDs to use as queries
            category: Product category ID
            database: Database identifier
            weights: Optional attribute weights for similarity calculation
            top_k: Number of results to return
            scope_product_ids: Optional list of product IDs to scope the search
            
        Returns:
            Dictionary containing search results
            
        Raises:
            Exception: If search operation fails
        """
        log = logger.bind(
            product_ids=query_product_ids,
            category=category,
            database=database,
            top_k=top_k,
        )
        
        start_time = time.time()
        
        try:
            # Get processor for the database
            processor = await registry.get_or_create(database)
            
            # Process weights - convert string keys to integers if needed
            processed_weights = self._process_weights(weights)
            
            # Execute search through processor queue
            results = await processor.queue_request(
                self._execute_product_search,
                query_product_ids,
                category,
                database,
                processed_weights,
                top_k,
                scope_product_ids,
            )
            
            process_time = time.time() - start_time
            log.info(
                "similar_products.success",
                process_time=f"{process_time:.4f}s",
                results_count=len(results),
            )
            
            return results
            
        except Exception as e:
            process_time = time.time() - start_time
            log.error(
                "similar_products.failed",
                error=str(e),
                process_time=f"{process_time:.4f}s"
            )
            raise
    
    async def search_similar_products_by_specs(
        self,
        specs: List[Dict[str, str]],
        product_category_id: int,
        database: str,
        weights: Optional[Dict[str, float]] = None,
        top_k: int = 5,
        scope_query: Optional[str] = None,
    ) -> Dict:
        """
        Search for similar products using product specifications.
        
        Args:
            specs: List of product specifications
            product_category_id: Product category ID
            database: Database identifier
            weights: Optional attribute weights for similarity calculation
            top_k: Number of results to return
            scope_query: Optional SQL query to scope the search
            
        Returns:
            Dictionary containing search results
            
        Raises:
            NotImplementedError: This method is not yet implemented
        """
        # TODO: Implement specs-based search
        raise NotImplementedError("Specs-based search not implemented yet")
    
    def _process_weights(self, weights: Optional[Dict[str, float]]) -> Optional[Dict[int, float]]:
        """
        Process and transform weights dictionary.
        
        Args:
            weights: Optional weights dictionary with string keys
            
        Returns:
            Processed weights dictionary with integer keys, or None
        """
        if weights is not None and len(weights) > 0:
            return {int(k): v for k, v in weights.items()}
        return None
    
    async def _execute_product_search(
        self,
        query_product_ids: List[int],
        category: int,
        database: str,
        weights: Optional[Dict[int, float]],
        top_k: int,
        scope_product_ids: Optional[List[int]],
    ) -> Dict:
        """
        Execute the actual product search operation.
        
        This is the core search function that gets queued through the processor.
        """
        processor = await registry.get_or_create(database)
        
        return await asyncio.to_thread(
            processor.inference.search_with_exist_products,
            query_product_ids,
            category,
            weights,
            top_k,
            scope_product_ids,
        )
