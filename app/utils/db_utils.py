from typing import List
from sqlalchemy import create_engine
import pandas as pd
import numpy as np
import os
from dotenv import load_dotenv

load_dotenv()
BASE_DIRECTORY = os.getenv("BASE_DIRECTORY")


def _sqlite_path(database: str) -> str:
    return os.path.join(BASE_DIRECTORY or "", database, "database.sqlite")


def get_numbers_of_categories(product_ids: List[int], database: str) -> List[int]:
    """Return distinct product_category_id values for provided product IDs."""
    engine = create_engine(f"sqlite:///{_sqlite_path(database)}", echo=False)
    with engine.connect() as conn:
        sql_product_id = "({})".format(", ".join(map(str, product_ids)))
        sql = (
            f"select distinct(product_category_id) as product_category_id "
            f"from product where product_id in {sql_product_id}"
        )
        df = pd.read_sql_query(sql, conn)
        # Return as a simple Python list of ints
        return df["product_category_id"].astype(int).tolist()


def check_existence_of_product_ids(product_ids: List[int], database: str) -> List[int]:
    """Return the subset of product_ids that do NOT exist in the database."""
    if not product_ids:
        return []
    engine = create_engine(f"sqlite:///{_sqlite_path(database)}", echo=False)
    with engine.connect() as conn:
        sql_product_id = "({})".format(", ".join(map(str, product_ids)))
        sql = f"select product_id from product where product_id in {sql_product_id}"
        pids = pd.read_sql_query(sql, conn)["product_id"].to_numpy()
        pids = np.atleast_1d(pids)
        missing = list(set(product_ids) - set(pids.tolist()))
        return missing


def check_existence_of_attribute_ids(attribute_ids: List[int], category: int, database: str):
    """
    For the given category, check which attribute_ids are missing from attribute_order.
    Returns:
      -1 if all attribute_ids are missing; otherwise, a set of missing attribute IDs
    """
    engine = create_engine(f"sqlite:///{_sqlite_path(database)}", echo=False)
    with engine.connect() as conn:
        sql = (
            f"select attribute_id from attribute_order where product_category_id={int(category)}"
        )
        atrid = pd.read_sql_query(sql, conn)["attribute_id"].to_numpy()
        missing = set(attribute_ids) - set(atrid.tolist())
        if len(attribute_ids) == len(missing):
            return -1
        return missing

